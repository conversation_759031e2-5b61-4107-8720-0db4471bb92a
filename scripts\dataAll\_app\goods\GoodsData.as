package dataAll._app.goods
{
   import UI.api.shop.IO_ShopObjectFather;
   import UI.api.shop.ShopBuyObject;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.oldUtils.StringDate;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.goods.define.PriceType;
   import dataAll._app.parts.PartsCreator;
   import dataAll._app.vip.define.VipLevelDefine;
   import dataAll._player.PlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.equip.EquipData;
   import dataAll.equip.save.EquipSave;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.equip.vehicle.VehicleDataCreator;
   import dataAll.gift.GiftAddit;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   
   public class GoodsData implements IO_ShopObjectFather
   {

      public static var MAX_NUM:int = 9999;

      public static var levelCoinIncome:Number = 1;

      public static var levelCoinIncomeNoMin:Number = 1;

      public static var freePriceMode:Boolean = false;
      
      public var def:GoodsDefine;
      
      public var playerData:PlayerData;
      
      public var showTextType:String = "";
      
      private var maxNumLimit:int = MAX_NUM;
      
      public var numDisFun:Function = null;
      
      private var _nowNum:String = "";
      
      public function GoodsData()
      {
         super();
         this.nowNum = 1;
      }
      
      public static function getDataByDefine(d0:GoodsDefine) : GoodsData
      {
         var da0:GoodsData = new GoodsData();
         da0.def = d0;
         da0.playerData = Gaming.PG.da;
         return da0;
      }
      
      public function getShopObj() : ShopBuyObject
      {
         var obj0:ShopBuyObject = this.def.getShopObj(1);
         obj0.setFather(this);
         obj0.price = this.getOnePrice();
         obj0.count = this.getApiNum();
         return obj0;
      }
      
      public function getPrice(beforeB0:Boolean = false) : Number
      {
         var v0:Number = beforeB0 ? this.getBeforeOnePrice() : this.getOnePrice();
         return v0 * this.nowNum;
      }
      
      public function getAlertPrice(beforeB0:Boolean = false) : Number
      {
         var v0:Number = beforeB0 ? this.getBeforeOnePrice() : this.getOnePrice();
         return v0 * this.getApiNum();
      }
      
      public function getOnePrice() : Number
      {
         if(GoodsData.freePriceMode)
         {
            return 0;
         }
         var v0:Number = this.getBeforeOnePrice();
         if(this.def.isTrueDiscountB())
         {
            v0 = this.def.getDiscountPrice();
         }
         return v0;
      }
      
      private function getBeforeOnePrice() : Number
      {
         var v0:Number = this.def.price;
         if(this.def.priceExtra != "")
         {
            v0 = Math.ceil(v0 * GoodsData[this.def.priceExtra]);
         }
         return v0;
      }
      
      public function copy() : GoodsData
      {
         var da0:GoodsData = new GoodsData();
         da0.def = this.def;
         da0.playerData = this.playerData;
         da0.nowNum = this.nowNum;
         da0.showTextType = this.showTextType;
         da0.maxNumLimit = this.maxNumLimit;
         da0.numDisFun = this.numDisFun;
         return da0;
      }
      
      public function set nowNum(v0:Number) : void
      {
         this._nowNum = Sounto64.encode(String(v0));
      }
      
      public function get nowNum() : Number
      {
         return Number(Sounto64.decode(this._nowNum));
      }
      
      public function getTrueNum() : int
      {
         return this.nowNum * this.def.num;
      }
      
      public function getApiNum() : int
      {
         return this.nowNum - this.getNumDiscount();
      }
      
      public function getNumDiscount() : int
      {
         if(this.numDisFun is Function)
         {
            return this.numDisFun(this.nowNum);
         }
         return 0;
      }
      
      public function getNumDiscountStr() : String
      {
         var num0:int = this.getNumDiscount();
         if(num0 > 0)
         {
            return "已优惠" + ComMethod.color(num0 * this.getOnePrice() + "","#FFFF00") + PriceType.getCnName(this.def.priceType);
         }
         return "";
      }
      
      public function getLevelString() : String
      {
         if(this.def.dataType == "vehicle")
         {
            return this.playerData.level + "";
         }
         return "";
      }
      
      public function getTipCnName() : String
      {
         return this.def.cnName;
      }
      
      public function getGatherTip() : String
      {
         var thingsDef0:ThingsDefine = null;
         var tempVehicleData0:VehicleData = null;
         var tempPartsData0:ThingsData = null;
         var equipData0:EquipData = null;
         var nowAllBuyNum0:int = 0;
         var vipHaveB0:Boolean = false;
         var str0:String = "";
         if(this.def.dataType == "things")
         {
            thingsDef0 = Gaming.defineGroup.things.getDefine(this.def.defineLabel);
            str0 = "<blue " + thingsDef0.getGatherTip(this.playerData) + "/>";
         }
         else
         {
            str0 = this.def.description;
         }
         if(this.def.autoUseB)
         {
            str0 += "\n\n<orange 购买后自动使用。/>";
         }
         if(Boolean(this.def.priceExtra))
         {
            str0 += "\n\n<green 该商品价格随着人物等级的提升而提高。/>";
         }
         if(this.def.dataType != "things")
         {
            if(this.def.dataType == "vehicle")
            {
               tempVehicleData0 = VehicleDataCreator.getTempData(Gaming.defineGroup.vehicle.getDefine(this.def.defineLabel),this.playerData);
               str0 = VehicleDataCreator.getTip(tempVehicleData0);
            }
            else if(this.def.dataType == "parts")
            {
               tempPartsData0 = PartsCreator.getTempData(this.def.defineLabel);
               str0 = tempPartsData0.getGatherTip();
            }
            else if(this.def.dataType == "equip")
            {
               equipData0 = this.getTempEquipData();
               str0 = equipData0.getGatherTip();
            }
         }
         if(this.def.buyLimitNum > 0)
         {
            nowAllBuyNum0 = this.playerData.goods.save.getAllBuyNum(this.def.name);
            str0 += "\n\n<orange <b>该商品只能购买" + nowAllBuyNum0 + "/" + this.def.buyLimitNum + "次</b>/>";
         }
         else if(this.def.dayBuyLimitNum > 0)
         {
            vipHaveB0 = VipLevelDefine.buyNameArr.indexOf(this.def.name) >= 0;
            str0 += "\n\n<orange <b>该商品每日限购" + (vipHaveB0 ? "，VIP等级越高可购买数量越多。" : "。") + "</b>/>";
         }
         if(this.def.endTime != "")
         {
            str0 += "\n\n<purple <b>该商品将于" + this.def.getOverDay(Gaming.api.save.getNowServerDate()) + "天后下架</b>/>";
         }
         return str0;
      }
      
      public function getTempArmsData() : ArmsData
      {
         return Gaming.defineGroup.armsCreator.getSuperDataByArmsRangeName(this.def.defineLabel,this.playerData);
      }
      
      public function getTempEquipData() : EquipData
      {
         var s0:EquipSave = GiftAddit.getEquipSaveByName(this.def.defineLabel,this.playerData.level);
         var da0:EquipData = new EquipData();
         da0.inData_bySave(s0,this.playerData);
         return da0;
      }
      
      public function getAlertInfoText() : String
      {
         var cn0:String = null;
         if(this.showTextType == "fromCn")
         {
            return "你确定要" + this.def.cnName + "？";
         }
         if(this.showTextType == "daily")
         {
            return "你确定要补签？";
         }
         if(this.showTextType == "postBribe")
         {
            return "你确定要游说？";
         }
         if(this.showTextType == "loveGift")
         {
            return "你确定要增加赠送次数？";
         }
         if(this.showTextType == "addPetBag")
         {
            return "确定要" + this.def.cnName + "？";
         }
         if(this.showTextType == "moneyDonation")
         {
            return "请选择黄金贡献次数";
         }
         if(this.showTextType == "otherDonation")
         {
            return "请选择银币贡献次数";
         }
         cn0 = this.def.cnName;
         if(this.def.num > 1 && this.def.cnName.indexOf("x") == -1 && this.def.cnName.indexOf("个") == -1)
         {
            cn0 += "x" + this.def.num;
         }
         return "确定购买 " + ComMethod.color(cn0,"#FF9900") + " ？";
      }
      
      public function getAlertOtherText(now0:int, surplusSpace0:int, max0:int, allMax0:int = 99999) : String
      {
         var nowMax0:int = 0;
         var firstStr0:String = null;
         var str0:String = "";
         if(this.showTextType == "fromCn")
         {
            return ComMethod.color("今日剩余次数： " + max0 + " 次","#00FF00");
         }
         if(this.showTextType == "daily")
         {
            return ComMethod.color("今日剩余补签次数： " + max0 + " 次","#00FF00");
         }
         if(this.showTextType == "loveGift")
         {
            return ComMethod.color("今日还可增加赠送次数： " + max0 + " 次","#00FF00");
         }
         if(this.showTextType == "postBribe")
         {
            return ComMethod.color("今日还可游说 " + max0 + " 次","#00FF00");
         }
         if(this.showTextType == "addPetBag")
         {
            return ComMethod.color("请选择数量（最大:" + max0 + "）","#00FF00");
         }
         if(this.showTextType == "moneyDonation" || this.showTextType == "otherDonation")
         {
            return ComMethod.color("今日剩余" + (this.showTextType == "moneyDonation" ? "黄金" : "银币") + "贡献次数： " + max0 + " 次","#00FF00");
         }
         if(this.showTextType == "doubleHoliday")
         {
            return str0 + ("   " + ComMethod.color("剩余" + this.def.getActionName() + "次数：" + max0,max0 > 0 ? "#FF66FF" : "#FF0000"));
         }
         if(this.def.father != "other")
         {
            str0 = ComMethod.color("购买后背包剩余空位：" + surplusSpace0,surplusSpace0 >= 0 ? "#00FF00" : "#FF0000");
         }
         nowMax0 = 99999;
         firstStr0 = "今日";
         if(this.def.dayBuyLimitNum > 0)
         {
            nowMax0 = max0;
         }
         if(this.def.buyLimitNum > 0)
         {
            if(allMax0 < nowMax0)
            {
               nowMax0 = allMax0;
               firstStr0 = "";
            }
         }
         if(nowMax0 < 99999)
         {
            str0 += "   " + ComMethod.color(firstStr0 + "剩余" + this.def.getActionName() + "次数：" + nowMax0,nowMax0 > 0 ? "#FF66FF" : "#FF0000");
         }
         return str0;
      }
      
      public function getFThingsDefine() : ThingsDefine
      {
         var d0:ThingsDefine = null;
         if(this.def.dataType == "things")
         {
            d0 = Gaming.defineGroup.things.getDefine(this.def.defineLabel);
            if(Boolean(d0))
            {
               if(d0.getShowGiftB())
               {
                  return d0;
               }
            }
         }
         return null;
      }
      
      public function getShowB() : Boolean
      {
         if(this.isStartB() && !this.isOverB())
         {
            return true;
         }
         return false;
      }
      
      private function isStartB() : Boolean
      {
         var now0:StringDate = null;
         var start0:StringDate = null;
         if(this.def.startTime == "")
         {
            return true;
         }
         now0 = Gaming.api.save.getNowServerDate();
         start0 = new StringDate(this.def.startTime);
         return now0.reductionOne(start0) >= 0;
      }
      
      private function isOverB() : Boolean
      {
         var now0:StringDate = null;
         if(this.def.endTime == "")
         {
            return false;
         }
         now0 = Gaming.api.save.getNowServerDate();
         return this.def.getOverDay(now0) <= 0;
      }
      
      public function isOverAllBuyB() : Boolean
      {
         var now0:int = 0;
         var max0:int = this.def.buyLimitNum;
         if(max0 > 0)
         {
            if(Boolean(this.playerData))
            {
               now0 = this.playerData.goods.save.getAllBuyNum(this.def.name);
               return now0 >= max0;
            }
            return true;
         }
         return false;
      }
      
      public function setMaxNumLimit(num0:int) : void
      {
         this.maxNumLimit = num0;
      }
      
      public function getMaxNumLimit() : int
      {
         var max0:Number = NaN;
         var max2:Number = NaN;
         if(this.def.dayBuyLimitNum > 0 || this.def.buyLimitNum > 0)
         {
            max0 = MAX_NUM;
            if(this.def.dayBuyLimitNum > 0)
            {
               max0 = this.playerData.goods.getBuyLimitNum(this.def.name);
            }
            if(this.def.buyLimitNum > 0)
            {
               max2 = this.playerData.goods.getBuyLimitAll(this.def.name);
               if(max0 > max2)
               {
                  max0 = max2;
               }
            }
            return max0;
         }
         return this.maxNumLimit;
      }
      
      public function getAllMaxNumLimit() : int
      {
         if(this.def.buyLimitNum > 0)
         {
            return this.playerData.goods.getBuyLimitAll(this.def.name);
         }
         return this.maxNumLimit;
      }
      
      public function dealNuyNum(num0:int) : int
      {
         var max0:int = this.getMaxNumLimit();
         if(num0 > max0)
         {
            num0 = max0;
         }
         if(num0 < 0)
         {
            num0 = 0;
         }
         return num0;
      }
   }
}

